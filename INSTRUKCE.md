# Kniha Jízd Bot - Instrukce k použití

## Před prvním spuštěním

### 1. Vyplnění přihlašovacích údajů
Otevři soubor `config.py` a vyplň své Google přihlašovací údaje:

```python
# Přihlašovací údaje - VYPLŇ SVOJE ÚDAJE!
GOOGLE_EMAIL = "<EMAIL>"  # Sem zadej svůj Google email
GOOGLE_PASSWORD = "tvojeHeslo123"      # Sem zadej své Google heslo
```

### 2. Instalace Chrome prohlížeče
Program potřebuje Google Chrome. Pokud ho nemáš nainstalovaný, stáhni si ho z: https://www.google.com/chrome/

## Spuštění programu

### Způsob 1: Dvojklik na soubor
- Dvojklikni na `Kniha_jizd.py`

### Způsob 2: <PERSON><PERSON><PERSON>azová řádka
```bash
python Kniha_jizd.py
```

## Co program dělá

1. **Spustí Chrome prohlížeč** a přejde na intranet.img-management.cz
2. **Automaticky se přihlásí** pomocí tvých Google údajů
3. **Přejde na stránku s Excel soubory** a stáhne informace o všech souborech
4. **Zpracuje data:**
   - Extrahuje SPZ (prvních 7 znaků názvu souboru)
   - Zkontroluje datum poslední úpravy
   - Posoudí, zda bylo odevzdání v termínu (±5 dní od 25. dne v měsíci)
5. **Vytvoří/aktualizuje Excel soubor** `Kontrola knihy jízd.xlsx` s výsledky:
   - ✅ **Zelené řádky** = Splnil (odevzdáno v termínu)
   - ❌ **Červené řádky** = Nesplnil (odevzdáno mimo termín)

## Výstupní soubor

Program vytvoří soubor `Kontrola knihy jízd.xlsx` se sloupci:
- **SPZ** - Prvních 7 znaků názvu souboru
- **Název souboru** - Celý název Excel souboru
- **Autor** - Kdo soubor upravil
- **Datum úpravy** - Kdy byl soubor naposledy upraven
- **Status** - Splnil/Nesplnil
- **Poznámka** - Dodatečné informace

## Řešení problémů

### Program se nezapne
- Zkontroluj, že máš nainstalovaný Python a Chrome
- Zkontroluj, že jsou vyplněny přihlašovací údaje v `config.py`

### Přihlášení nefunguje
- Zkontroluj email a heslo v `config.py`
- Pokud máš zapnuté 2FA (dvoufaktorové ověření), program se zastaví a budeš muset dokončit ověření manuálně

### Program nenajde data
- Zkontroluj, že máš přístup k intranet.img-management.cz
- Možná se změnila struktura stránky - kontaktuj vývojáře

### Chrome se nezavře
- Zavři Chrome manuálně z Task Manageru

## Logy a ladění

Program vytváří log soubor `kniha_jizd_bot.log` kde najdeš detailní informace o běhu programu.

## Bezpečnost

⚠️ **DŮLEŽITÉ**: Soubor `config.py` obsahuje tvé heslo! 
- Nesdílej tento soubor s nikým
- Nenahávej ho na internet
- Zvažte použití aplikačního hesla místo hlavního hesla

## Automatické spouštění

Pokud chceš program spouštět automaticky (např. každý den), můžeš:
1. Vytvořit batch soubor (.bat)
2. Nastavit Windows Task Scheduler
3. Použít cron (Linux/Mac)

## Kontakt

Pokud máš problémy nebo návrhy na vylepšení, kontaktuj vývojáře.
