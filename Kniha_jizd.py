#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON><PERSON><PERSON> - Automatizace kontroly odevzdání knih jízd
Autor: Automaticky generován<PERSON>: 2025-09-15
"""

import os
import time
import logging
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Tuple, Optional
import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.webdriver.edge.service import Service as EdgeService
from selenium.webdriver.edge.options import Options as EdgeOptions
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.firefox import GeckoDriverManager
from webdriver_manager.microsoft import EdgeChromiumDriverManager
from openpyxl import Workbook, load_workbook
from openpyxl.styles import PatternFill, Font
import config

# Nastavení logování
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('kniha_jizd_bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class KnihaJizdBot:
    """Hlavní třída pro automatizaci kontroly knih jízd"""

    def __init__(self):
        self.driver = None
        self.wait = None
        self.data = []

    def setup_driver(self):
        """Nastavení WebDriver pro jakýkoliv dostupný prohlížeč"""
        browsers = [
            ("Chrome", self._setup_chrome),
            ("Firefox", self._setup_firefox),
            ("Edge", self._setup_edge)
        ]

        for browser_name, setup_func in browsers:
            try:
                logger.info(f"Zkouším nastavit {browser_name}...")
                if setup_func():
                    logger.info(f"{browser_name} úspěšně nastaven")
                    return True
            except Exception as e:
                logger.warning(f"Nepodařilo se nastavit {browser_name}: {e}")
                continue

        logger.error("Nepodařilo se nastavit žádný prohlížeč!")
        logger.error("Nainstaluj Chrome, Firefox nebo Edge")
        return False

    def _setup_chrome(self):
        """Nastavení Chrome"""
        try:
            options = ChromeOptions()
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-gpu")
            options.add_argument("--window-size=1920,1080")

            service = ChromeService(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=options)
            self.driver.implicitly_wait(config.IMPLICIT_WAIT)
            self.wait = WebDriverWait(self.driver, config.WEBDRIVER_TIMEOUT)
            return True
        except Exception:
            return False

    def _setup_firefox(self):
        """Nastavení Firefox"""
        try:
            options = FirefoxOptions()
            options.add_argument("--width=1920")
            options.add_argument("--height=1080")

            service = FirefoxService(GeckoDriverManager().install())
            self.driver = webdriver.Firefox(service=service, options=options)
            self.driver.implicitly_wait(config.IMPLICIT_WAIT)
            self.wait = WebDriverWait(self.driver, config.WEBDRIVER_TIMEOUT)
            return True
        except Exception:
            return False

    def _setup_edge(self):
        """Nastavení Edge"""
        try:
            options = EdgeOptions()
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-gpu")
            options.add_argument("--window-size=1920,1080")

            service = EdgeService(EdgeChromiumDriverManager().install())
            self.driver = webdriver.Edge(service=service, options=options)
            self.driver.implicitly_wait(config.IMPLICIT_WAIT)
            self.wait = WebDriverWait(self.driver, config.WEBDRIVER_TIMEOUT)
            return True
        except Exception:
            return False

    def login_with_google(self):
        """Automatické přihlášení na intranet"""
        try:
            logger.info("Zahajuji automatické přihlášení...")

            # Kontrola, zda jsou vyplněny přihlašovací údaje
            if not config.GOOGLE_USERNAME or not config.GOOGLE_PASSWORD:
                logger.error("Přihlašovací údaje nejsou vyplněny v config.py!")
                logger.error("Vyplň GOOGLE_USERNAME a GOOGLE_PASSWORD v souboru config.py")
                return False

            self.driver.get(config.LOGIN_URL)
            time.sleep(5)  # Čekáme na načtení stránky

            # Zkusíme najít přihlašovací formulář
            return self.fill_login_form()

        except Exception as e:
            logger.error(f"Chyba při přihlašování: {e}")
            return False

    def fill_login_form(self):
        """Vyplnění přihlašovacího formuláře"""
        try:
            logger.info("Hledám přihlašovací formulář...")

            # Čekáme na načtení přihlašovacího formuláře
            time.sleep(3)

            # Hledáme pole pro uživatelské jméno
            username_selectors = [
                "//input[@type='text']",
                "//input[@name='username']",
                "//input[@name='user']",
                "//input[@name='login']",
                "//input[@id='username']",
                "//input[@id='user']",
                "//input[@id='login']",
                "//input[contains(@placeholder, 'uživatel')]",
                "//input[contains(@placeholder, 'jméno')]",
                "//input[contains(@class, 'username')]",
                "//input[contains(@class, 'user')]"
            ]

            username_field = None
            for selector in username_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            username_field = element
                            break
                    if username_field:
                        break
                except:
                    continue

            if username_field:
                logger.info("Pole pro uživatelské jméno nalezeno, vyplňuji...")
                username_field.clear()
                username_field.send_keys(config.GOOGLE_USERNAME)
                time.sleep(1)
            else:
                logger.error("Pole pro uživatelské jméno nenalezeno!")
                return False

            # Hledáme pole pro heslo
            password_selectors = [
                "//input[@type='password']",
                "//input[@name='password']",
                "//input[@name='pass']",
                "//input[@id='password']",
                "//input[@id='pass']",
                "//input[contains(@placeholder, 'heslo')]",
                "//input[contains(@class, 'password')]"
            ]

            password_field = None
            for selector in password_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            password_field = element
                            break
                    if password_field:
                        break
                except:
                    continue

            if password_field:
                logger.info("Pole pro heslo nalezeno, vyplňuji...")
                password_field.clear()
                password_field.send_keys(config.GOOGLE_PASSWORD)
                time.sleep(1)
            else:
                logger.error("Pole pro heslo nenalezeno!")
                return False

            # Hledáme tlačítko pro přihlášení
            login_selectors = [
                "//button[contains(text(), 'Přihlásit')]",
                "//button[contains(text(), 'přihlásit')]",
                "//input[@type='submit']",
                "//button[@type='submit']",
                "//button[contains(text(), 'Login')]",
                "//button[contains(text(), 'Sign in')]",
                "//input[@value='Přihlásit']",
                "//input[@value='Login']",
                "//button[contains(@class, 'login')]",
                "//button[contains(@class, 'submit')]"
            ]

            login_button = None
            for selector in login_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            login_button = element
                            break
                    if login_button:
                        break
                except:
                    continue

            if login_button:
                logger.info("Tlačítko pro přihlášení nalezeno, klikám...")
                login_button.click()
                time.sleep(5)
            else:
                logger.warning("Tlačítko pro přihlášení nenalezeno, zkouším Enter...")
                # Zkusíme stisknout Enter v poli hesla
                password_field.send_keys(Keys.RETURN)
                time.sleep(5)

            # Čekáme na přihlášení a přesměrování
            timeout = 30
            start_time = time.time()

            while time.time() - start_time < timeout:
                current_url = self.driver.current_url

                # Kontrolujeme, zda jsme úspěšně přihlášeni
                if "login" not in current_url.lower() and "přihlášení" not in current_url.lower():
                    logger.info("Přihlášení úspěšné!")
                    return True

                # Kontrolujeme chybové zprávy
                try:
                    error_selectors = [
                        "//div[contains(@class, 'error')]",
                        "//div[contains(@class, 'alert')]",
                        "//span[contains(@class, 'error')]",
                        "//*[contains(text(), 'neplatné')]",
                        "//*[contains(text(), 'chyba')]",
                        "//*[contains(text(), 'error')]"
                    ]

                    for selector in error_selectors:
                        error_elements = self.driver.find_elements(By.XPATH, selector)
                        for error_element in error_elements:
                            if error_element.is_displayed():
                                error_text = error_element.text
                                if error_text:
                                    logger.error(f"Chyba při přihlášení: {error_text}")
                                    return False
                except:
                    pass

                time.sleep(2)

            logger.error("Timeout při čekání na přihlášení")
            return False

        except Exception as e:
            logger.error(f"Chyba při vyplňování přihlašovacího formuláře: {e}")
            return False

    def scrape_excel_data(self):
        """Stahování dat o Excel souborech z cílové URL"""
        try:
            logger.info("Naviguji na cílovou URL...")
            self.driver.get(config.TARGET_URL)
            time.sleep(5)

            # Čekáme na načtení stránky
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))

            page_number = 1
            total_files = 0

            while True:
                logger.info(f"Zpracovávám stránku {page_number}...")

                # Zpracujeme aktuální stránku
                files_on_page = self.scrape_current_page()
                total_files += files_on_page

                if files_on_page == 0:
                    logger.warning(f"Na stránce {page_number} nebyly nalezeny žádné soubory")

                # Zkusíme přejít na další stránku
                if not self.go_to_next_page():
                    logger.info("Dosažena poslední stránka")
                    break

                page_number += 1
                time.sleep(3)  # Čekáme na načtení další stránky

            logger.info(f"Celkem zpracováno {total_files} souborů z {page_number} stránek")
            return True

        except Exception as e:
            logger.error(f"Chyba při stahování dat: {e}")
            return False

    def scrape_current_page(self):
        """Zpracování aktuální stránky s Excel soubory"""
        try:
            files_found = 0

            # Hledáme tabulku s daty
            table_selectors = [
                "//table[contains(@class, 'ms-listviewtable')]",
                "//table[@class='ms-listviewtable']",
                "//div[contains(@class, 'ms-listviewtable')]//table",
                "//table",
                "//div[contains(@class, 'ms-listviewgrid')]//table"
            ]

            table = None
            for selector in table_selectors:
                try:
                    tables = self.driver.find_elements(By.XPATH, selector)
                    for t in tables:
                        if t.is_displayed():
                            table = t
                            break
                    if table:
                        break
                except:
                    continue

            if not table:
                logger.warning("Tabulka s daty nenalezena na této stránce")
                return 0

            # Hledáme řádky s daty
            rows = table.find_elements(By.XPATH, ".//tr")
            logger.info(f"Nalezeno {len(rows)} řádků v tabulce")

            for i, row in enumerate(rows):
                try:
                    # Přeskočíme hlavičku (obvykle první řádek)
                    if i == 0:
                        continue

                    cells = row.find_elements(By.XPATH, ".//td")
                    if len(cells) < 3:  # Musíme mít alespoň 3 sloupce
                        continue

                    # Podle obrázku: sloupce jsou Typ, Název, Změněno, Autor změny
                    # Název je ve druhém sloupci (index 1)
                    filename_cell = cells[1] if len(cells) > 1 else cells[0]

                    # Hledáme odkaz nebo text s názvem souboru
                    filename = ""
                    try:
                        filename_link = filename_cell.find_element(By.XPATH, ".//a")
                        filename = filename_link.text.strip()
                    except:
                        filename = filename_cell.text.strip()

                    if not filename or filename == "":
                        continue

                    # Filtrujeme pouze Excel soubory (.xls, .xlsx)
                    if not (filename.lower().endswith('.xls') or filename.lower().endswith('.xlsx') or
                           'xls' in filename.lower() or any(char.isdigit() for char in filename)):
                        continue

                    # Extrahujeme SPZ (prvních 7 znaků)
                    spz = filename[:7] if len(filename) >= 7 else filename

                    # Datum změny je ve třetím sloupci (index 2)
                    date_text = ""
                    if len(cells) > 2:
                        date_text = cells[2].text.strip()

                    # Autor je ve čtvrtém sloupci (index 3)
                    author = ""
                    if len(cells) > 3:
                        author = cells[3].text.strip()
                    else:
                        author = "Neznámý"

                    # Pokusíme se parsovat datum
                    date_modified = self.parse_date(date_text)

                    # Přidáme data do seznamu
                    self.data.append({
                        'filename': filename,
                        'spz': spz,
                        'author': author,
                        'date_modified': date_modified,
                        'date_text': date_text
                    })

                    files_found += 1
                    logger.info(f"Zpracován soubor: {filename} (SPZ: {spz}) - {author} - {date_text}")

                except Exception as e:
                    logger.warning(f"Chyba při zpracování řádku {i}: {e}")
                    continue

            logger.info(f"Na této stránce nalezeno {files_found} souborů")
            return files_found

        except Exception as e:
            logger.error(f"Chyba při zpracování stránky: {e}")
            return 0

    def go_to_next_page(self):
        """Přechod na další stránku"""
        try:
            # Hledáme tlačítko "Další" nebo šipku pro další stránku
            next_selectors = [
                "//a[contains(@title, 'Další')]",
                "//a[contains(@title, 'Next')]",
                "//a[contains(text(), 'Další')]",
                "//a[contains(text(), 'Next')]",
                "//a[contains(@class, 'ms-commandLink') and contains(@title, 'Další')]",
                "//img[@alt='Další']/..",
                "//span[contains(text(), '>')]/parent::a",
                "//a[contains(@href, 'PageFirstRow')]",
                "//input[@title='Další stránka']",
                "//a[@class='ms-paging']"
            ]

            next_button = None
            for selector in next_selectors:
                try:
                    buttons = self.driver.find_elements(By.XPATH, selector)
                    for button in buttons:
                        if button.is_displayed() and button.is_enabled():
                            # Zkontrolujeme, že tlačítko není disabled
                            if "disabled" not in button.get_attribute("class").lower():
                                next_button = button
                                break
                    if next_button:
                        break
                except:
                    continue

            if next_button:
                logger.info("Přecházím na další stránku...")
                next_button.click()
                time.sleep(3)
                return True
            else:
                logger.info("Tlačítko 'Další' nenalezeno - pravděpodobně jsme na poslední stránce")
                return False

        except Exception as e:
            logger.warning(f"Chyba při přechodu na další stránku: {e}")
            return False

    def parse_date(self, date_text: str) -> Optional[datetime]:
        """Parsování data z různých formátů"""
        date_formats = [
            "%d.%m.%Y %H:%M",
            "%d.%m.%Y",
            "%d/%m/%Y %H:%M",
            "%d/%m/%Y",
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%d",
            "%d-%m-%Y %H:%M",
            "%d-%m-%Y"
        ]

        for fmt in date_formats:
            try:
                return datetime.strptime(date_text, fmt)
            except ValueError:
                continue

        logger.warning(f"Nepodařilo se parsovat datum: {date_text}")
        return None

    def check_submission_status(self, date_modified: Optional[datetime]) -> Tuple[bool, str]:
        """Kontrola, zda bylo odevzdání v termínu (±5 dní od měsíčního termínu)"""
        if not date_modified:
            return False, "Nesplnil - neplatné datum"

        # Aktuální datum
        now = datetime.now()

        # Najdeme nejbližší měsíční termín (25. den v měsíci)
        # Pokud jsme před 25., bereme aktuální měsíc, jinak další měsíc
        if now.day <= 25:
            target_month = now.month
            target_year = now.year
        else:
            if now.month == 12:
                target_month = 1
                target_year = now.year + 1
            else:
                target_month = now.month + 1
                target_year = now.year

        # Vytvoříme cílové datum (25. den cílového měsíce)
        target_date = datetime(target_year, target_month, 25)

        # Rozmezí ±5 dní
        start_range = target_date - timedelta(days=config.TOLERANCE_DAYS)
        end_range = target_date + timedelta(days=config.TOLERANCE_DAYS)

        # Kontrola, zda datum úpravy spadá do rozmezí
        if start_range <= date_modified <= end_range:
            return True, "Splnil"
        else:
            return False, "Nesplnil"

    def create_or_update_excel(self):
        """Vytvoření nebo aktualizace Excel souboru s výsledky"""
        try:
            # Zkusíme načíst existující soubor
            if os.path.exists(config.EXCEL_OUTPUT_FILE):
                logger.info("Načítám existující Excel soubor...")
                workbook = load_workbook(config.EXCEL_OUTPUT_FILE)
                worksheet = workbook.active
            else:
                logger.info("Vytvářím nový Excel soubor...")
                workbook = Workbook()
                worksheet = workbook.active
                worksheet.title = "Kontrola knih jízd"

                # Vytvoříme hlavičku
                headers = ["SPZ", "Název souboru", "Autor", "Datum úpravy", "Status", "Poznámka"]
                for col, header in enumerate(headers, 1):
                    worksheet.cell(row=1, column=col, value=header)
                    # Tučné písmo pro hlavičku
                    worksheet.cell(row=1, column=col).font = Font(bold=True)

            # Vyčistíme existující data (kromě hlavičky)
            if worksheet.max_row > 1:
                worksheet.delete_rows(2, worksheet.max_row)

            # Přidáme nová data
            for row_idx, item in enumerate(self.data, 2):
                spz = item['spz']
                filename = item['filename']
                author = item['author']
                date_modified = item['date_modified']
                date_text = item['date_text']

                # Kontrola statusu
                is_compliant, status = self.check_submission_status(date_modified)

                # Zapíšeme data do řádku
                worksheet.cell(row=row_idx, column=1, value=spz)
                worksheet.cell(row=row_idx, column=2, value=filename)
                worksheet.cell(row=row_idx, column=3, value=author)
                worksheet.cell(row=row_idx, column=4, value=date_text)
                worksheet.cell(row=row_idx, column=5, value=status)

                # Poznámka s rozmezím
                if date_modified:
                    note = f"Kontrola pro {date_modified.strftime('%d.%m.%Y')}"
                else:
                    note = "Neplatné datum"
                worksheet.cell(row=row_idx, column=6, value=note)

                # Barevné formátování
                if is_compliant:
                    # Zelené pozadí pro splněné
                    fill = PatternFill(start_color=config.COLOR_GREEN, end_color=config.COLOR_GREEN, fill_type="solid")
                    font = Font(color=config.COLOR_BLACK)
                else:
                    # Červené pozadí pro nesplněné
                    fill = PatternFill(start_color=config.COLOR_RED, end_color=config.COLOR_RED, fill_type="solid")
                    font = Font(color=config.COLOR_BLACK)

                # Aplikujeme formátování na celý řádek
                for col in range(1, 7):
                    cell = worksheet.cell(row=row_idx, column=col)
                    cell.fill = fill
                    cell.font = font

            # Upravíme šířku sloupců
            column_widths = [15, 40, 20, 20, 15, 30]
            for col, width in enumerate(column_widths, 1):
                worksheet.column_dimensions[worksheet.cell(row=1, column=col).column_letter].width = width

            # Uložíme soubor
            workbook.save(config.EXCEL_OUTPUT_FILE)
            logger.info(f"Excel soubor uložen: {config.EXCEL_OUTPUT_FILE}")

            return True

        except Exception as e:
            logger.error(f"Chyba při práci s Excel souborem: {e}")
            return False

    def cleanup(self):
        """Ukončení a úklid"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("WebDriver ukončen")
            except Exception as e:
                logger.error(f"Chyba při ukončování WebDriver: {e}")

    def run(self):
        """Hlavní metoda pro spuštění celého procesu"""
        try:
            logger.info("=== Spouštím Kniha Jízd Bot ===")

            # 1. Nastavení WebDriver
            if not self.setup_driver():
                logger.error("Nepodařilo se nastavit WebDriver")
                return False

            # 2. Přihlášení
            if not self.login_with_google():
                logger.error("Nepodařilo se přihlásit")
                return False

            # 3. Stahování dat
            if not self.scrape_excel_data():
                logger.error("Nepodařilo se stáhnout data")
                return False

            # 4. Vytvoření/aktualizace Excel souboru
            if not self.create_or_update_excel():
                logger.error("Nepodařilo se vytvořit Excel soubor")
                return False

            logger.info("=== Kniha Jízd Bot úspěšně dokončen ===")
            logger.info(f"Výsledky uloženy do: {config.EXCEL_OUTPUT_FILE}")
            logger.info(f"Zpracováno celkem {len(self.data)} souborů")

            return True

        except Exception as e:
            logger.error(f"Neočekávaná chyba: {e}")
            return False
        finally:
            self.cleanup()


def main():
    """Hlavní funkce programu"""
    bot = KnihaJizdBot()
    success = bot.run()

    if success:
        print("\n✅ Program úspěšně dokončen!")
        print(f"📊 Výsledky najdete v souboru: {config.EXCEL_OUTPUT_FILE}")
    else:
        print("\n❌ Program skončil s chybou!")
        print("📋 Zkontrolujte log soubor: kniha_jizd_bot.log")

    input("\nStiskněte Enter pro ukončení...")


if __name__ == "__main__":
    main()