#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON><PERSON><PERSON> - Automatizace kontroly odevzdání knih jízd
Autor: <PERSON><PERSON> generov<PERSON>: 2025-09-15
"""

import os
import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from openpyxl import Workbook, load_workbook
from openpyxl.styles import PatternFill, Font
import config

# Nastavení logování
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('kniha_jizd_bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class KnihaJizdBot:
    """Hlavní třída pro automatizaci kontroly knih jízd"""

    def __init__(self):
        self.driver = None
        self.wait = None
        self.data = []

    def setup_driver(self):
        """Nastavení Chrome WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            # chrome_options.add_argument("--headless")  # Odkomentuj pro běh na pozadí

            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.implicitly_wait(config.IMPLICIT_WAIT)
            self.wait = WebDriverWait(self.driver, config.WEBDRIVER_TIMEOUT)

            logger.info("WebDriver úspěšně nastaven")
            return True

        except Exception as e:
            logger.error(f"Chyba při nastavování WebDriver: {e}")
            return False

    def login_with_google(self):
        """Přihlášení přes Google OAuth"""
        try:
            logger.info("Zahajuji přihlášení...")
            self.driver.get(config.LOGIN_URL)

            # Čekáme na načtení stránky a hledáme Google login tlačítko
            time.sleep(3)

            # Hledáme různé možné selektory pro Google login
            google_selectors = [
                "//a[contains(@href, 'google')]",
                "//button[contains(text(), 'Google')]",
                "//input[contains(@value, 'Google')]",
                "//div[contains(@class, 'google')]//a",
                "//span[contains(text(), 'Google')]/..",
                "//img[contains(@alt, 'Google')]/.."
            ]

            google_button = None
            for selector in google_selectors:
                try:
                    google_button = self.driver.find_element(By.XPATH, selector)
                    if google_button:
                        break
                except:
                    continue

            if google_button:
                logger.info("Google login tlačítko nalezeno")
                google_button.click()

                # Čekáme na přesměrování na Google
                self.wait.until(lambda driver: "google" in driver.current_url.lower())
                logger.info("Přesměrováno na Google OAuth")

                # Zde by uživatel musel manuálně dokončit přihlášení
                # Čekáme na návrat na původní doménu
                logger.info("Čekám na dokončení přihlášení uživatelem...")

                # Čekáme až se vrátíme na intranet
                timeout = 300  # 5 minut na přihlášení
                start_time = time.time()

                while time.time() - start_time < timeout:
                    current_url = self.driver.current_url
                    if "intranet.img-management.cz" in current_url and "google" not in current_url.lower():
                        logger.info("Přihlášení úspěšné!")
                        return True
                    time.sleep(2)

                logger.error("Timeout při čekání na přihlášení")
                return False

            else:
                logger.error("Google login tlačítko nenalezeno")
                # Pokusíme se pokračovat bez explicitního kliknutí
                logger.info("Pokračuji bez kliknutí na Google tlačítko...")
                return True

        except Exception as e:
            logger.error(f"Chyba při přihlašování: {e}")
            return False

    def scrape_excel_data(self):
        """Stahování dat o Excel souborech z cílové URL"""
        try:
            logger.info("Naviguji na cílovou URL...")
            self.driver.get(config.TARGET_URL)
            time.sleep(5)

            # Čekáme na načtení tabulky
            table_selectors = [
                "//table",
                "//div[contains(@class, 'ms-listviewtable')]",
                "//table[contains(@class, 'ms-listviewtable')]",
                "//div[contains(@class, 'ms-listviewgrid')]"
            ]

            table = None
            for selector in table_selectors:
                try:
                    table = self.wait.until(EC.presence_of_element_located((By.XPATH, selector)))
                    if table:
                        break
                except:
                    continue

            if not table:
                logger.error("Tabulka s daty nenalezena")
                return False

            logger.info("Tabulka nalezena, extrahuji data...")

            # Hledáme řádky s daty
            rows = table.find_elements(By.XPATH, ".//tr")

            for row in rows[1:]:  # Přeskočíme hlavičku
                try:
                    cells = row.find_elements(By.XPATH, ".//td")
                    if len(cells) >= 3:  # Minimálně název, autor, datum

                        # Extrahujeme název souboru
                        filename_cell = cells[0]
                        filename_link = filename_cell.find_element(By.XPATH, ".//a")
                        filename = filename_link.text.strip() if filename_link else filename_cell.text.strip()

                        # Extrahujeme SPZ (prvních 7 znaků)
                        spz = filename[:7] if len(filename) >= 7 else filename

                        # Extrahujeme datum úpravy (obvykle v posledním sloupci)
                        date_text = cells[-1].text.strip()

                        # Pokusíme se parsovat datum
                        date_modified = self.parse_date(date_text)

                        # Extrahujeme autora (obvykle předposlední sloupec)
                        author = cells[-2].text.strip() if len(cells) > 1 else "Neznámý"

                        self.data.append({
                            'filename': filename,
                            'spz': spz,
                            'author': author,
                            'date_modified': date_modified,
                            'date_text': date_text
                        })

                        logger.info(f"Zpracován soubor: {filename} (SPZ: {spz})")

                except Exception as e:
                    logger.warning(f"Chyba při zpracování řádku: {e}")
                    continue

            logger.info(f"Celkem zpracováno {len(self.data)} souborů")
            return True

        except Exception as e:
            logger.error(f"Chyba při stahování dat: {e}")
            return False

    def parse_date(self, date_text: str) -> Optional[datetime]:
        """Parsování data z různých formátů"""
        date_formats = [
            "%d.%m.%Y %H:%M",
            "%d.%m.%Y",
            "%d/%m/%Y %H:%M",
            "%d/%m/%Y",
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%d",
            "%d-%m-%Y %H:%M",
            "%d-%m-%Y"
        ]

        for fmt in date_formats:
            try:
                return datetime.strptime(date_text, fmt)
            except ValueError:
                continue

        logger.warning(f"Nepodařilo se parsovat datum: {date_text}")
        return None

    def check_submission_status(self, date_modified: Optional[datetime]) -> Tuple[bool, str]:
        """Kontrola, zda bylo odevzdání v termínu (±5 dní od měsíčního termínu)"""
        if not date_modified:
            return False, "Nesplnil - neplatné datum"

        # Aktuální datum
        now = datetime.now()

        # Najdeme nejbližší měsíční termín (25. den v měsíci)
        # Pokud jsme před 25., bereme aktuální měsíc, jinak další měsíc
        if now.day <= 25:
            target_month = now.month
            target_year = now.year
        else:
            if now.month == 12:
                target_month = 1
                target_year = now.year + 1
            else:
                target_month = now.month + 1
                target_year = now.year

        # Vytvoříme cílové datum (25. den cílového měsíce)
        target_date = datetime(target_year, target_month, 25)

        # Rozmezí ±5 dní
        start_range = target_date - timedelta(days=config.TOLERANCE_DAYS)
        end_range = target_date + timedelta(days=config.TOLERANCE_DAYS)

        # Kontrola, zda datum úpravy spadá do rozmezí
        if start_range <= date_modified <= end_range:
            return True, "Splnil"
        else:
            return False, "Nesplnil"

    def create_or_update_excel(self):
        """Vytvoření nebo aktualizace Excel souboru s výsledky"""
        try:
            # Zkusíme načíst existující soubor
            if os.path.exists(config.EXCEL_OUTPUT_FILE):
                logger.info("Načítám existující Excel soubor...")
                workbook = load_workbook(config.EXCEL_OUTPUT_FILE)
                worksheet = workbook.active
            else:
                logger.info("Vytvářím nový Excel soubor...")
                workbook = Workbook()
                worksheet = workbook.active
                worksheet.title = "Kontrola knih jízd"

                # Vytvoříme hlavičku
                headers = ["SPZ", "Název souboru", "Autor", "Datum úpravy", "Status", "Poznámka"]
                for col, header in enumerate(headers, 1):
                    worksheet.cell(row=1, column=col, value=header)
                    # Tučné písmo pro hlavičku
                    worksheet.cell(row=1, column=col).font = Font(bold=True)

            # Vyčistíme existující data (kromě hlavičky)
            if worksheet.max_row > 1:
                worksheet.delete_rows(2, worksheet.max_row)

            # Přidáme nová data
            for row_idx, item in enumerate(self.data, 2):
                spz = item['spz']
                filename = item['filename']
                author = item['author']
                date_modified = item['date_modified']
                date_text = item['date_text']

                # Kontrola statusu
                is_compliant, status = self.check_submission_status(date_modified)

                # Zapíšeme data do řádku
                worksheet.cell(row=row_idx, column=1, value=spz)
                worksheet.cell(row=row_idx, column=2, value=filename)
                worksheet.cell(row=row_idx, column=3, value=author)
                worksheet.cell(row=row_idx, column=4, value=date_text)
                worksheet.cell(row=row_idx, column=5, value=status)

                # Poznámka s rozmezím
                if date_modified:
                    note = f"Kontrola pro {date_modified.strftime('%d.%m.%Y')}"
                else:
                    note = "Neplatné datum"
                worksheet.cell(row=row_idx, column=6, value=note)

                # Barevné formátování
                if is_compliant:
                    # Zelené pozadí pro splněné
                    fill = PatternFill(start_color=config.COLOR_GREEN, end_color=config.COLOR_GREEN, fill_type="solid")
                    font = Font(color=config.COLOR_BLACK)
                else:
                    # Červené pozadí pro nesplněné
                    fill = PatternFill(start_color=config.COLOR_RED, end_color=config.COLOR_RED, fill_type="solid")
                    font = Font(color=config.COLOR_BLACK)

                # Aplikujeme formátování na celý řádek
                for col in range(1, 7):
                    cell = worksheet.cell(row=row_idx, column=col)
                    cell.fill = fill
                    cell.font = font

            # Upravíme šířku sloupců
            column_widths = [15, 40, 20, 20, 15, 30]
            for col, width in enumerate(column_widths, 1):
                worksheet.column_dimensions[worksheet.cell(row=1, column=col).column_letter].width = width

            # Uložíme soubor
            workbook.save(config.EXCEL_OUTPUT_FILE)
            logger.info(f"Excel soubor uložen: {config.EXCEL_OUTPUT_FILE}")

            return True

        except Exception as e:
            logger.error(f"Chyba při práci s Excel souborem: {e}")
            return False

    def cleanup(self):
        """Ukončení a úklid"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("WebDriver ukončen")
            except Exception as e:
                logger.error(f"Chyba při ukončování WebDriver: {e}")

    def run(self):
        """Hlavní metoda pro spuštění celého procesu"""
        try:
            logger.info("=== Spouštím Kniha Jízd Bot ===")

            # 1. Nastavení WebDriver
            if not self.setup_driver():
                logger.error("Nepodařilo se nastavit WebDriver")
                return False

            # 2. Přihlášení
            if not self.login_with_google():
                logger.error("Nepodařilo se přihlásit")
                return False

            # 3. Stahování dat
            if not self.scrape_excel_data():
                logger.error("Nepodařilo se stáhnout data")
                return False

            # 4. Vytvoření/aktualizace Excel souboru
            if not self.create_or_update_excel():
                logger.error("Nepodařilo se vytvořit Excel soubor")
                return False

            logger.info("=== Kniha Jízd Bot úspěšně dokončen ===")
            logger.info(f"Výsledky uloženy do: {config.EXCEL_OUTPUT_FILE}")
            logger.info(f"Zpracováno celkem {len(self.data)} souborů")

            return True

        except Exception as e:
            logger.error(f"Neočekávaná chyba: {e}")
            return False
        finally:
            self.cleanup()


def main():
    """Hlavní funkce programu"""
    bot = KnihaJizdBot()
    success = bot.run()

    if success:
        print("\n✅ Program úspěšně dokončen!")
        print(f"📊 Výsledky najdete v souboru: {config.EXCEL_OUTPUT_FILE}")
    else:
        print("\n❌ Program skončil s chybou!")
        print("📋 Zkontrolujte log soubor: kniha_jizd_bot.log")

    input("\nStiskněte Enter pro ukončení...")


if __name__ == "__main__":
    main()