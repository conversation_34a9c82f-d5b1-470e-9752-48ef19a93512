@echo off
echo ========================================
echo    Kniha Jizd Bot - Automaticka kontrola
echo ========================================
echo.

REM Kontrola, zda existuje Python
python --version >nul 2>&1
if errorlevel 1 (
    echo CHYBA: Python neni nainstalovan!
    echo Stahni a nainstaluj Python z https://www.python.org/
    pause
    exit /b 1
)

REM Kontrola, zda existuje config.py
if not exist "config.py" (
    echo CHYBA: Soubor config.py nenalezen!
    echo Ujisti se, ze jsi ve spravne slozce.
    pause
    exit /b 1
)

REM Kontrola, zda jsou vyplneny prihlasovaci udaje
findstr /C:"GOOGLE_EMAIL = \"\"" config.py >nul
if not errorlevel 1 (
    echo CHYBA: Pri<PERSON>asovaci udaje nejsou vyplneny!
    echo Otevri config.py a vyplň GOOGLE_EMAIL a GOOGLE_PASSWORD
    pause
    exit /b 1
)

echo Spoustim Kniha Jizd Bot...
echo.

REM Spusteni programu
python Kniha_jizd.py

echo.
echo Program dokoncen.
pause
